from django import template
from django.forms import <PERSON><PERSON><PERSON><PERSON>
from django.utils.html import format_html, escape
from django.utils.safestring import mark_safe
import json

register = template.Library()


@register.simple_tag
def matter_field(field, **kwargs):
    """
    Render a Django form field using Matter CSS styling.
    Supports text inputs, textareas, and select fields.

    Usage:
    {% matter_field form.field_name class="small" type="email" %}
    {% matter_field form.field_name attrs='{"hx-get": "/api/endpoint/", "hx-target": "#result"}' %}
    {% matter_field form.field_name class="small" attrs='{"hx-get": "/api/", "data-value": "test"}' %}
    """
    if not isinstance(field, BoundField):
        return ""

    # Extract special parameters
    css_class = kwargs.get('class', '')
    placeholder = kwargs.get('placeholder', field.label or '')
    attrs_param = kwargs.get('attrs', {})

    # Parse attrs if it's a string (JSON)
    if isinstance(attrs_param, str):
        try:
            attrs_param = json.loads(attrs_param)
        except (json.JSONDecodeError, ValueError):
            attrs_param = {}

    # Build additional attributes from kwargs
    attrs = {}
    for key, value in kwargs.items():
        if key not in ['class', 'placeholder', 'attrs']:
            # Convert underscores to hyphens for HTML attributes
            html_key = key.replace('_', '-')
            attrs[html_key] = value

    # Merge attrs parameter
    attrs.update(attrs_param)

    # Determine field type and render accordingly
    widget_type = field.field.widget.__class__.__name__.lower()

    if 'select' in widget_type:
        return render_matter_select(field, css_class, attrs)
    elif 'textarea' in widget_type:
        return render_matter_textarea(field, css_class, placeholder, attrs)
    else:
        return render_matter_input(field, css_class, placeholder, attrs)


def build_attrs_string(attrs):
    """Build HTML attributes string from dictionary, escaping values."""
    if not attrs:
        return ''

    attr_parts = []
    for key, value in attrs.items():
        if value is not None and value != '':
            # Escape the value for HTML safety
            escaped_value = str(value).replace('"', '&quot;')
            attr_parts.append(f'{key}="{escaped_value}"')

    return ' '.join(attr_parts)


def render_matter_input(field, css_class, placeholder, attrs):
    """Render a Matter CSS text input field."""

    # Separate container attributes from field attributes
    container_attrs = {}
    field_attrs = {}

    for key, value in attrs.items():
        if key in ['hx-swap-oob', 'hx-swap']:
            container_attrs[key] = value
        else:
            field_attrs[key] = value

    # Build attributes strings
    container_attrs_str = build_attrs_string(container_attrs)
    field_attrs_str = build_attrs_string(field_attrs)

    # Get field value
    value = field.value() if field.value() is not None else ''

    # Build required attribute
    required = 'required' if field.field.required else ''

    # Check for small variant
    small_class = 'small' if 'small' in css_class else ''

    # Determine input type
    input_type = field_attrs.get('type', 'text')
    if input_type == 'text':  # Only auto-detect if type not explicitly set
        if 'email' in field.name.lower():
            input_type = 'email'
        elif 'password' in field.name.lower():
            input_type = 'password'
        elif 'number' in field.name.lower() or 'age' in field.name.lower():
            input_type = 'number'
        elif 'date' in field.name.lower():
            input_type = 'date'

    html = f'''
    <div class="matter-textfield-outlined {small_class} {css_class}" id="{field.id_for_label}_container" {container_attrs_str}>
        <input
            type="{input_type}"
            name="{field.name}"
            id="{field.id_for_label}"
            value="{value}"
            placeholder=" "
            {required}
            {field_attrs_str}
        >
        <span>{field.label}</span>
    </div>
    '''
    
    # Add error messages if any
    if field.errors:
        html += '<div class="matter-field-errors">'
        for error in field.errors:
            html += f'<div class="matter-error-text">{error}</div>'
        html += '</div>'
    
    return mark_safe(html)


def render_matter_textarea(field, css_class, placeholder, attrs):
    """Render a Matter CSS textarea field."""

    # Separate container attributes from field attributes
    container_attrs = {}
    field_attrs = {}

    for key, value in attrs.items():
        if key in ['hx-swap-oob', 'hx-swap']:
            container_attrs[key] = value
        else:
            field_attrs[key] = value

    # Build attributes strings
    container_attrs_str = build_attrs_string(container_attrs)
    field_attrs_str = build_attrs_string(field_attrs)

    # Get field value
    value = field.value() if field.value() is not None else ''

    # Build required attribute
    required = 'required' if field.field.required else ''

    # Check for small variant
    small_class = 'small' if 'small' in css_class else ''

    html = f'''
    <div class="matter-textfield-outlined {small_class} {css_class}" id="{field.id_for_label}_container" {container_attrs_str}>
        <textarea
            name="{field.name}"
            id="{field.id_for_label}"
            placeholder=" "
            {required}
            {field_attrs_str}
        >{value}</textarea>
        <span>{field.label}</span>
    </div>
    '''
    
    # Add error messages if any
    if field.errors:
        html += '<div class="matter-field-errors">'
        for error in field.errors:
            html += f'<div class="matter-error-text">{error}</div>'
        html += '</div>'
    
    return mark_safe(html)


def render_matter_select(field, css_class, attrs):
    """Render a Matter CSS select field."""

    # Separate container attributes from field attributes
    container_attrs = {}
    field_attrs = {}

    for key, value in attrs.items():
        if key in ['hx-swap-oob', 'hx-swap']:
            container_attrs[key] = value
        else:
            field_attrs[key] = value

    # Build attributes strings
    container_attrs_str = build_attrs_string(container_attrs)
    field_attrs_str = build_attrs_string(field_attrs)

    # Get field value
    selected_value = field.value() if field.value() is not None else ''

    # Build required attribute
    required = 'required' if field.field.required else ''

    # Check for small variant
    small_class = 'small' if 'small' in css_class else ''

    # Get choices
    choices = field.field.choices

    html = f'''
    <div class="matter-select-wrapper {small_class} {css_class}" id="{field.id_for_label}_container" {container_attrs_str}>
        <select
            name="{field.name}"
            id="{field.id_for_label}"
            class="matter-select"
            {required}
            {field_attrs_str}
        >
    '''
    
    # Add empty option if not required or no value selected
    if not field.field.required or not selected_value:
        html += '<option value="">------</option>'
    
    # Add options
    for value, label in choices:
        if value == '':  # Skip empty choice if already added
            continue
        selected_attr = 'selected' if str(value) == str(selected_value) else ''
        html += f'<option value="{value}" {selected_attr}>{label}</option>'
    
    html += f'''
        </select>
        <label class="matter-select-label">{field.label}</label>
    </div>
    '''
    
    # Add JavaScript for dropdown arrow animation
    html += f'''
    <script>
        (function() {{
            const wrapper = document.getElementById('{field.id_for_label}_container');
            const select = document.getElementById('{field.id_for_label}');

            if (wrapper && select) {{
                let isOpen = false;

                // Handle select opening
                select.addEventListener('mousedown', function() {{
                    isOpen = !isOpen;
                    wrapper.setAttribute('data-open', isOpen);
                }});

                // Handle select closing
                select.addEventListener('blur', function() {{
                    isOpen = false;
                    wrapper.setAttribute('data-open', 'false');
                }});

                // Handle value changes to manage label floating
                select.addEventListener('change', function() {{
                    if (select.value) {{
                        select.classList.add('has-value');
                    }} else {{
                        select.classList.remove('has-value');
                    }}
                }});

                // Set initial state
                if (select.value) {{
                    select.classList.add('has-value');
                }}
            }}
        }})();
    </script>
    '''
    
    # Add error messages if any
    if field.errors:
        html += '<div class="matter-field-errors">'
        for error in field.errors:
            html += f'<div class="matter-error-text">{error}</div>'
        html += '</div>'
    
    return mark_safe(html)


@register.simple_tag
def matter_field_htmx(field, **kwargs):
    """
    Convenience template tag for Matter fields with HTMX attributes.
    Automatically converts underscore-separated HTMX attributes to hyphenated ones.

    Usage:
    {% matter_field_htmx form.field_name class="small" hx_get="/api/endpoint/" hx_target="#result" hx_trigger="keyup delay:500ms" %}
    """
    # Convert HTMX underscore attributes to hyphens
    htmx_attrs = {}
    other_kwargs = {}

    for key, value in kwargs.items():
        if key.startswith('hx_') or key.startswith('data_'):
            # Convert hx_get to hx-get, data_value to data-value, etc.
            html_key = key.replace('_', '-')
            htmx_attrs[html_key] = value
        else:
            other_kwargs[key] = value

    # Add HTMX attributes to the attrs parameter
    if htmx_attrs:
        existing_attrs = other_kwargs.get('attrs', {})
        if isinstance(existing_attrs, str):
            try:
                existing_attrs = json.loads(existing_attrs)
            except (json.JSONDecodeError, ValueError):
                existing_attrs = {}

        existing_attrs.update(htmx_attrs)
        other_kwargs['attrs'] = existing_attrs

    return matter_field(field, **other_kwargs)
